# YUE智能体综合应用平台

## 项目简介

YUE智能体综合应用平台是一个企业级的AI智能体平台，提供多种智能体服务：

- 🤖 智能客服智能体
- 📊 Text2SQL数据分析智能体
- 📚 企业级知识库问答智能体
- ✍️ 企业内部文案创作智能体

## 核心功能

```mermaid
mindmap
  root((YUE智能体平台))
    智能客服
      业务系统对接
      工具调用
      多轮对话
      问题解答
    Text2SQL分析
      自然语言转SQL
      数据查询执行
      图表可视化
      结果解释
    知识库问答
      传统RAG
      NanoGraphRAG
      多模态RAG
      精确召回
    文案创作
      模板选择
      内容生成
      格式导出
      质量优化
```

## 系统架构

```mermaid
graph TB
    subgraph "前端层 (Frontend)"
        A[React + TypeScript]
        A1[智能客服界面]
        A2[Text2SQL分析界面]
        A3[知识库问答界面]
        A4[文案创作界面]
        A --> A1
        A --> A2
        A --> A3
        A --> A4
    end

    subgraph "API网关层"
        B[FastAPI + Socket.IO]
        B1[RESTful API]
        B2[WebSocket实时通信]
        B3[身份认证]
        B --> B1
        B --> B2
        B --> B3
    end

    subgraph "业务逻辑层"
        C[智能体服务]
        C1[智能客服智能体]
        C2[Text2SQL智能体]
        C3[知识库问答智能体]
        C4[文案创作智能体]
        C --> C1
        C --> C2
        C --> C3
        C --> C4
    end

    subgraph "数据存储层"
        D1[MySQL<br/>用户数据/会话历史]
        D2[Milvus<br/>向量数据库]
        D3[MinIO<br/>文件存储]
        D4[Redis<br/>缓存/队列]
    end

    subgraph "AI服务层"
        E1[OpenAI API<br/>大语言模型]
        E2[LangChain<br/>AI应用框架]
        E3[RAG系统<br/>检索增强生成]
        E4[向量化服务<br/>Embeddings]
    end

    subgraph "基础设施层"
        F1[Docker容器]
        F2[Nginx负载均衡]
        F3[监控日志]
        F4[CI/CD流水线]
    end

    A --> B
    B --> C
    C --> D1
    C --> D2
    C --> D3
    C --> D4
    C --> E1
    C --> E2
    C --> E3
    C --> E4

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D1 fill:#fff3e0
    style D2 fill:#fff3e0
    style D3 fill:#fff3e0
    style D4 fill:#fff3e0
    style E1 fill:#fce4ec
    style E2 fill:#fce4ec
    style E3 fill:#fce4ec
    style E4 fill:#fce4ec
```

## 技术架构

### 前端技术栈
- React 18 + TypeScript
- Vite (构建工具)
- Tailwind CSS + Framer Motion
- Zustand (状态管理)
- React Query (数据获取)
- Socket.io (实时通信)

### 后端技术栈
- Python 3.11+ + FastAPI
- SQLAlchemy + Alembic
- MySQL (关系数据库)
- Milvus (向量数据库)
- MinIO (文件存储)
- Redis (缓存)
- Celery (异步任务)

## 项目结构

### 目录结构概览

```
yue_ai_agent/
├── frontend/                 # 前端项目
├── backend/                  # 后端项目
├── docker/                   # Docker配置
├── docs/                     # 项目文档
├── scripts/                  # 部署脚本
└── README.md
```

### 详细目录结构

```mermaid
graph LR
    subgraph "项目根目录"
        A[yue_ai_agent/]
    end

    subgraph "前端模块"
        B[frontend/]
        B1[src/components/]
        B2[src/pages/]
        B3[src/services/]
        B4[src/store/]
        B5[public/]
        B --> B1
        B --> B2
        B --> B3
        B --> B4
        B --> B5
    end

    subgraph "后端模块"
        C[backend/]
        C1[app/api/]
        C2[app/agents/]
        C3[app/models/]
        C4[app/services/]
        C5[app/core/]
        C --> C1
        C --> C2
        C --> C3
        C --> C4
        C --> C5
    end

    subgraph "智能体模块"
        D[agents/]
        D1[customer_service/]
        D2[text2sql/]
        D3[knowledge_base/]
        D4[content_creation/]
        D --> D1
        D --> D2
        D --> D3
        D --> D4
    end

    subgraph "配置文件"
        E[配置文件]
        E1[docker-compose.yml]
        E2[.env.example]
        E3[requirements.txt]
        E4[package.json]
        E --> E1
        E --> E2
        E --> E3
        E --> E4
    end

    subgraph "文档目录"
        F[docs/]
        F1[project-structure.md]
        F2[quick-start.md]
        F3[development.md]
        F --> F1
        F --> F2
        F --> F3
    end

    A --> B
    A --> C
    A --> E
    A --> F
    C2 --> D

    style A fill:#f9f9f9
    style B fill:#e1f5fe
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#f3e5f5
    style F fill:#fce4ec
```

## 快速开始

### 环境要求
- Node.js 18+
- Python 3.11+
- Docker & Docker Compose
- MySQL 8.0+
- Redis 6.0+

### 安装依赖

#### 前端
```bash
cd frontend
npm install
```

#### 后端
```bash
cd backend
pip install -r requirements.txt
```

### 启动服务

#### 使用Docker Compose (推荐)
```bash
docker-compose up -d
```

#### 手动启动
```bash
# 启动后端
cd backend
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 启动前端
cd frontend
npm run dev
```

## 开发指南

详细的开发指南请参考 [docs/development.md](docs/development.md)

## 部署指南

详细的部署指南请参考 [docs/deployment.md](docs/deployment.md)

## 许可证

MIT License
