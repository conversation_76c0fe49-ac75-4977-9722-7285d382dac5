"""
智能客服智能体核心逻辑
"""

import re
import json
from typing import Dict, Any, List, Optional, Tuple
from sqlalchemy.orm import Session
from datetime import datetime

from app.services.customer_service import (
    ProductService, PromotionService, OrderService, 
    RefundService, ComplaintService, UserService
)
from app.schemas.customer_service import (
    ScenarioResponse, ProductInquiryResponse, PromotionInquiryResponse,
    OrderTrackingScenarioResponse, RefundRequestResponse, 
    ComplaintResponse, TransferResponse
)

class CustomerServiceAgent:
    """智能客服智能体"""
    
    def __init__(self, db: Session):
        self.db = db
        self.product_service = ProductService()
        self.promotion_service = PromotionService()
        self.order_service = OrderService()
        self.refund_service = RefundService()
        self.complaint_service = ComplaintService()
        self.user_service = UserService()
        
        # 意图识别模式
        self.intent_patterns = {
            "product_inquiry": [
                r"产品|商品|货品|物品",
                r"价格|多少钱|费用",
                r"规格|参数|尺寸|材质",
                r"库存|有货|现货",
                r"推荐|建议|哪个好"
            ],
            "promotion_inquiry": [
                r"优惠|折扣|活动|促销",
                r"优惠券|代金券|券码",
                r"满减|打折|特价",
                r"会员|VIP|特权"
            ],
            "order_tracking": [
                r"订单|单号",
                r"物流|快递|配送",
                r"跟踪|追踪|查询",
                r"到哪了|状态|进度"
            ],
            "refund_request": [
                r"退货|退款|换货",
                r"不满意|质量问题|不合适",
                r"申请退|要退|想退"
            ],
            "complaint": [
                r"投诉|抱怨|不满",
                r"问题|故障|错误",
                r"建议|意见|反馈"
            ],
            "transfer_to_human": [
                r"人工|客服|真人",
                r"转接|转人工|找客服"
            ]
        }
    
    def identify_intent(self, message: str) -> str:
        """识别用户意图"""
        message_lower = message.lower()
        
        # 计算每个意图的匹配分数
        intent_scores = {}
        for intent, patterns in self.intent_patterns.items():
            score = 0
            for pattern in patterns:
                if re.search(pattern, message_lower):
                    score += 1
            intent_scores[intent] = score
        
        # 返回得分最高的意图
        if intent_scores:
            best_intent = max(intent_scores, key=intent_scores.get)
            if intent_scores[best_intent] > 0:
                return best_intent
        
        return "general_inquiry"
    
    def extract_entities(self, message: str, intent: str) -> Dict[str, Any]:
        """提取实体信息"""
        entities = {}
        
        # 提取订单号
        order_pattern = r"[A-Z0-9]{10,20}"
        order_matches = re.findall(order_pattern, message)
        if order_matches:
            entities["order_number"] = order_matches[0]
        
        # 提取产品名称（简化版）
        if intent == "product_inquiry":
            # 这里可以使用更复杂的NER模型
            entities["product_query"] = message
        
        # 提取优惠券代码
        coupon_pattern = r"[A-Z0-9]{6,12}"
        coupon_matches = re.findall(coupon_pattern, message)
        if coupon_matches and intent == "promotion_inquiry":
            entities["coupon_code"] = coupon_matches[0]
        
        return entities
    
    def process_message(self, message: str, user_id: int, context: Optional[Dict[str, Any]] = None) -> ScenarioResponse:
        """处理用户消息"""
        # 识别意图
        intent = self.identify_intent(message)
        
        # 提取实体
        entities = self.extract_entities(message, intent)
        
        # 根据意图处理
        if intent == "product_inquiry":
            return self._handle_product_inquiry(message, entities, user_id, context)
        elif intent == "promotion_inquiry":
            return self._handle_promotion_inquiry(message, entities, user_id, context)
        elif intent == "order_tracking":
            return self._handle_order_tracking(message, entities, user_id, context)
        elif intent == "refund_request":
            return self._handle_refund_request(message, entities, user_id, context)
        elif intent == "complaint":
            return self._handle_complaint(message, entities, user_id, context)
        elif intent == "transfer_to_human":
            return self._handle_transfer_to_human(message, entities, user_id, context)
        else:
            return self._handle_general_inquiry(message, entities, user_id, context)
    
    def _handle_product_inquiry(self, message: str, entities: Dict[str, Any], user_id: int, context: Optional[Dict[str, Any]]) -> ProductInquiryResponse:
        """处理产品咨询"""
        query = entities.get("product_query", message)
        
        # 搜索产品
        products = self.product_service.search_products(self.db, query, limit=3)
        
        if not products:
            return ProductInquiryResponse(
                intent="product_inquiry",
                response="抱歉，没有找到相关产品。请您提供更具体的产品名称或型号，我来为您查询。",
                suggestions=["请提供产品名称", "查看热门推荐", "联系人工客服"]
            )
        
        # 获取第一个产品的详细信息
        product = products[0]
        inventory = self.product_service.get_product_inventory(self.db, product.id)
        
        # 构建回复
        response_parts = [
            f"您好！为您找到了【{product.name}】的信息：",
            f"💰 价格：¥{product.price}",
        ]
        
        if product.material:
            response_parts.append(f"🏷️ 材质：{product.material}")
        
        if inventory:
            if inventory.stock_quantity > 0:
                response_parts.append("✅ 库存状态：有货")
            else:
                response_parts.append("❌ 库存状态：暂时缺货")
        
        if product.size_guide:
            response_parts.append("📏 尺寸指南：请参考商品详情页的尺码表")
        
        response_parts.append("请问您还想了解其他信息吗？")
        
        # 准备推荐产品
        recommendations = []
        if len(products) > 1:
            for p in products[1:]:
                recommendations.append({
                    "id": p.id,
                    "name": p.name,
                    "price": float(p.price)
                })
        
        return ProductInquiryResponse(
            intent="product_inquiry",
            response="\n".join(response_parts),
            data={
                "product": {
                    "id": product.id,
                    "name": product.name,
                    "price": float(product.price),
                    "material": product.material,
                    "in_stock": inventory.stock_quantity > 0 if inventory else False
                },
                "recommendations": recommendations
            },
            actions=[
                {"type": "view_product", "product_id": product.id, "label": "查看详情"},
                {"type": "add_to_cart", "product_id": product.id, "label": "加入购物车"}
            ],
            suggestions=["查看更多规格", "了解优惠活动", "查看用户评价"]
        )
    
    def _handle_promotion_inquiry(self, message: str, entities: Dict[str, Any], user_id: int, context: Optional[Dict[str, Any]]) -> PromotionInquiryResponse:
        """处理促销活动咨询"""
        # 获取当前活动
        promotions = self.promotion_service.get_active_promotions(self.db)
        
        # 检查用户是否为VIP
        vip_benefits = self.user_service.get_user_vip_benefits(self.db, user_id)
        
        response_parts = ["您好！当前我们有以下优惠活动："]
        
        if promotions:
            for i, promo in enumerate(promotions[:3], 1):
                response_parts.append(f"{i}. 🎉 {promo.name}")
                if promo.description:
                    response_parts.append(f"   {promo.description}")
        else:
            response_parts.append("暂时没有进行中的促销活动。")
        
        # 添加VIP权益信息
        if vip_benefits["is_vip"]:
            response_parts.append(f"\n🌟 作为VIP{vip_benefits['level']}会员，您还可享受：")
            for benefit in vip_benefits["benefits"]:
                response_parts.append(f"   • {benefit}")
        
        # 处理优惠券查询
        coupon_code = entities.get("coupon_code")
        if coupon_code:
            coupon = self.promotion_service.get_coupon_by_code(self.db, coupon_code)
            if coupon:
                response_parts.append(f"\n✅ 优惠券【{coupon_code}】有效！")
                response_parts.append(f"   折扣：{coupon.discount_value}{'%' if coupon.discount_type == 'percentage' else '元'}")
                if coupon.min_order_amount:
                    response_parts.append(f"   使用条件：订单满¥{coupon.min_order_amount}")
            else:
                response_parts.append(f"\n❌ 优惠券【{coupon_code}】无效或已过期")
        
        response_parts.append("\n请问您还想了解其他优惠信息吗？")
        
        return PromotionInquiryResponse(
            intent="promotion_inquiry",
            response="\n".join(response_parts),
            data={
                "promotions": [{"id": p.id, "name": p.name, "description": p.description} for p in promotions],
                "vip_benefits": vip_benefits,
                "coupon_valid": coupon_code and self.promotion_service.get_coupon_by_code(self.db, coupon_code) is not None
            },
            actions=[
                {"type": "view_promotions", "label": "查看所有活动"},
                {"type": "get_coupons", "label": "领取优惠券"}
            ],
            suggestions=["查看会员权益", "了解积分规则", "获取专属优惠"]
        )
    
    def _handle_order_tracking(self, message: str, entities: Dict[str, Any], user_id: int, context: Optional[Dict[str, Any]]) -> OrderTrackingScenarioResponse:
        """处理订单跟踪"""
        order_number = entities.get("order_number")
        
        if not order_number:
            return OrderTrackingScenarioResponse(
                intent="order_tracking",
                response="请提供您的订单号，我来帮您查询订单状态。订单号通常是10-20位的字母数字组合。",
                suggestions=["在订单页面查找", "查看邮件通知", "联系人工客服"]
            )
        
        # 查询订单
        order = self.order_service.get_order_by_number(self.db, order_number)
        
        if not order:
            return OrderTrackingScenarioResponse(
                intent="order_tracking",
                response=f"抱歉，没有找到订单号【{order_number}】的相关信息。请检查订单号是否正确，或联系客服协助查询。",
                suggestions=["重新输入订单号", "查看历史订单", "联系人工客服"]
            )
        
        # 检查订单归属
        if order.user_id != user_id:
            return OrderTrackingScenarioResponse(
                intent="order_tracking",
                response="抱歉，该订单不属于您的账户。请确认订单号是否正确。",
                suggestions=["重新输入订单号", "登录正确账户", "联系人工客服"]
            )
        
        # 构建订单状态回复
        status_map = {
            "pending_payment": "待付款",
            "paid_pending_ship": "已付款，待发货",
            "shipped": "已发货",
            "delivered": "已签收",
            "cancelled": "已取消",
            "refunded": "已退款"
        }
        
        response_parts = [
            f"您的订单【{order_number}】查询结果：",
            f"📦 订单状态：{status_map.get(order.status.value, order.status.value)}",
            f"💰 订单金额：¥{order.total_amount}",
            f"📅 下单时间：{order.created_at.strftime('%Y-%m-%d %H:%M')}"
        ]
        
        # 获取物流信息
        if order.status in ["shipped", "delivered"]:
            logistics = self.order_service.get_logistics_tracking(self.db, order.id)
            if logistics:
                response_parts.extend([
                    f"🚚 物流公司：{logistics.logistics_company}",
                    f"📋 运单号：{logistics.tracking_number}",
                    f"📍 当前状态：{logistics.current_status}"
                ])
                
                if logistics.current_location:
                    response_parts.append(f"📍 当前位置：{logistics.current_location}")
                
                if order.estimated_delivery:
                    response_parts.append(f"⏰ 预计送达：{order.estimated_delivery.strftime('%Y-%m-%d')}")
        
        response_parts.append("\n如需更多帮助，请随时联系我们！")
        
        actions = []
        if order.status == "shipped":
            actions.append({"type": "track_logistics", "tracking_number": order.tracking_number, "label": "详细物流跟踪"})
        elif order.status == "delivered":
            actions.append({"type": "rate_order", "order_id": order.id, "label": "评价订单"})
            actions.append({"type": "apply_refund", "order_id": order.id, "label": "申请售后"})
        
        return OrderTrackingScenarioResponse(
            intent="order_tracking",
            response="\n".join(response_parts),
            data={
                "order": {
                    "id": order.id,
                    "order_number": order.order_number,
                    "status": order.status.value,
                    "total_amount": float(order.total_amount),
                    "created_at": order.created_at.isoformat()
                }
            },
            actions=actions,
            suggestions=["查看订单详情", "联系物流公司", "申请售后服务"]
        )
