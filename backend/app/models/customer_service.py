"""
智能客服相关数据模型
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, Enum, JSON
from sqlalchemy.types import Numeric
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum
from datetime import datetime

from app.core.database import Base


class OrderStatus(enum.Enum):
    """订单状态枚举"""
    PENDING_PAYMENT = "pending_payment"  # 待付款
    PAID_PENDING_SHIP = "paid_pending_ship"  # 已付款待发货
    SHIPPED = "shipped"  # 已发货
    DELIVERED = "delivered"  # 已签收
    CANCELLED = "cancelled"  # 已取消
    REFUNDED = "refunded"  # 已退款


class RefundStatus(enum.Enum):
    """退换货状态枚举"""
    PENDING = "pending"  # 待处理
    APPROVED = "approved"  # 已同意
    REJECTED = "rejected"  # 已拒绝
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"  # 已完成


class ComplaintStatus(enum.Enum):
    """投诉状态枚举"""
    OPEN = "open"  # 待处理
    IN_PROGRESS = "in_progress"  # 处理中
    RESOLVED = "resolved"  # 已解决
    CLOSED = "closed"  # 已关闭


class PromotionType(enum.Enum):
    """促销类型枚举"""
    DISCOUNT = "discount"  # 折扣
    FULL_REDUCTION = "full_reduction"  # 满减
    GIFT = "gift"  # 赠品
    FREE_SHIPPING = "free_shipping"  # 包邮


# 产品相关表
class Category(Base):
    """产品分类表"""
    __tablename__ = "categories"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    parent_id = Column(Integer, ForeignKey("categories.id"))
    description = Column(Text)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    
    # 关系
    products = relationship("Product", back_populates="category")


class Product(Base):
    """产品表"""
    __tablename__ = "products"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    model = Column(String(100), index=True)
    sku = Column(String(100), unique=True, index=True)
    description = Column(Text)
    specifications = Column(JSON)  # JSON格式存储规格
    price = Column(Numeric(10, 2), nullable=False)
    original_price = Column(Numeric(10, 2))  # 原价
    material = Column(String(255))
    size_guide = Column(Text)  # 尺寸指南
    images = Column(JSON)  # 产品图片URLs
    category_id = Column(Integer, ForeignKey("categories.id"))
    is_active = Column(Boolean, default=True)
    is_featured = Column(Boolean, default=False)  # 是否推荐
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关系
    category = relationship("Category", back_populates="products")
    inventory = relationship("Inventory", back_populates="product", uselist=False)
    order_items = relationship("OrderItem", back_populates="product")


class Inventory(Base):
    """库存表"""
    __tablename__ = "inventory"
    
    id = Column(Integer, primary_key=True, index=True)
    product_id = Column(Integer, ForeignKey("products.id"), unique=True)
    stock_quantity = Column(Integer, default=0)
    reserved_quantity = Column(Integer, default=0)  # 预留库存
    available_quantity = Column(Integer, default=0)  # 可用库存
    low_stock_threshold = Column(Integer, default=10)  # 低库存阈值
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关系
    product = relationship("Product", back_populates="inventory")


# 订单相关表
class Order(Base):
    """订单表"""
    __tablename__ = "orders"
    
    id = Column(Integer, primary_key=True, index=True)
    order_number = Column(String(50), unique=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    status = Column(Enum(OrderStatus), default=OrderStatus.PENDING_PAYMENT)
    total_amount = Column(Numeric(10, 2))
    discount_amount = Column(Numeric(10, 2), default=0)  # 优惠金额
    shipping_fee = Column(Numeric(10, 2), default=0)  # 运费
    final_amount = Column(Numeric(10, 2))  # 最终金额
    shipping_address = Column(JSON)  # JSON格式存储地址信息
    shipping_method = Column(String(100))
    payment_method = Column(String(100))
    notes = Column(Text)  # 订单备注
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关系
    user = relationship("User", back_populates="orders")
    order_items = relationship("OrderItem", back_populates="order")
    logistics = relationship("LogisticsTracking", back_populates="order", uselist=False)
    refund_requests = relationship("RefundRequest", back_populates="order")


class OrderItem(Base):
    """订单商品表"""
    __tablename__ = "order_items"
    
    id = Column(Integer, primary_key=True, index=True)
    order_id = Column(Integer, ForeignKey("orders.id"))
    product_id = Column(Integer, ForeignKey("products.id"))
    quantity = Column(Integer, nullable=False)
    unit_price = Column(Numeric(10, 2))
    total_price = Column(Numeric(10, 2))
    specifications = Column(JSON)  # 商品规格（颜色、尺寸等）
    
    # 关系
    order = relationship("Order", back_populates="order_items")
    product = relationship("Product", back_populates="order_items")


class LogisticsTracking(Base):
    """物流跟踪表"""
    __tablename__ = "logistics_tracking"
    
    id = Column(Integer, primary_key=True, index=True)
    order_id = Column(Integer, ForeignKey("orders.id"), unique=True)
    tracking_number = Column(String(100), index=True)
    logistics_company = Column(String(100))
    logistics_company_code = Column(String(50))  # 物流公司代码
    current_status = Column(String(100))
    estimated_delivery = Column(DateTime)
    actual_delivery = Column(DateTime)
    tracking_info = Column(JSON)  # 详细跟踪信息
    last_updated = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关系
    order = relationship("Order", back_populates="logistics")


# 促销活动相关表
class Promotion(Base):
    """促销活动表"""
    __tablename__ = "promotions"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    type = Column(Enum(PromotionType))
    rules = Column(JSON)  # 促销规则（满减条件、折扣比例等）
    start_time = Column(DateTime)
    end_time = Column(DateTime)
    is_active = Column(Boolean, default=True)
    priority = Column(Integer, default=0)  # 优先级
    created_at = Column(DateTime, default=func.now())
    
    # 关系
    coupons = relationship("Coupon", back_populates="promotion")


class Coupon(Base):
    """优惠券表"""
    __tablename__ = "coupons"
    
    id = Column(Integer, primary_key=True, index=True)
    code = Column(String(50), unique=True, index=True)
    name = Column(String(255))
    promotion_id = Column(Integer, ForeignKey("promotions.id"))
    discount_type = Column(String(20))  # percentage, fixed_amount
    discount_value = Column(Numeric(10, 2))
    min_order_amount = Column(Numeric(10, 2))  # 最低订单金额
    max_discount_amount = Column(Numeric(10, 2))  # 最大优惠金额
    usage_limit = Column(Integer)  # 使用次数限制
    used_count = Column(Integer, default=0)  # 已使用次数
    start_time = Column(DateTime)
    end_time = Column(DateTime)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    
    # 关系
    promotion = relationship("Promotion", back_populates="coupons")
    user_coupons = relationship("UserCoupon", back_populates="coupon")


class UserCoupon(Base):
    """用户优惠券表"""
    __tablename__ = "user_coupons"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    coupon_id = Column(Integer, ForeignKey("coupons.id"))
    is_used = Column(Boolean, default=False)
    used_at = Column(DateTime)
    order_id = Column(Integer, ForeignKey("orders.id"))
    obtained_at = Column(DateTime, default=func.now())
    
    # 关系
    user = relationship("User", back_populates="user_coupons")
    coupon = relationship("Coupon", back_populates="user_coupons")


# 售后服务相关表
class RefundRequest(Base):
    """退换货申请表"""
    __tablename__ = "refund_requests"
    
    id = Column(Integer, primary_key=True, index=True)
    request_number = Column(String(50), unique=True, index=True)
    order_id = Column(Integer, ForeignKey("orders.id"))
    user_id = Column(Integer, ForeignKey("users.id"))
    type = Column(String(20))  # refund, exchange
    reason = Column(String(255))
    description = Column(Text)
    status = Column(Enum(RefundStatus), default=RefundStatus.PENDING)
    refund_amount = Column(Numeric(10, 2))
    evidence_images = Column(JSON)  # 证据图片
    admin_notes = Column(Text)  # 管理员备注
    processed_by = Column(Integer, ForeignKey("users.id"))
    processed_at = Column(DateTime)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关系
    order = relationship("Order", back_populates="refund_requests")
    user = relationship("User", foreign_keys=[user_id])
    processor = relationship("User", foreign_keys=[processed_by])


class Complaint(Base):
    """投诉建议表"""
    __tablename__ = "complaints"
    
    id = Column(Integer, primary_key=True, index=True)
    complaint_number = Column(String(50), unique=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    type = Column(String(50))  # complaint, suggestion
    category = Column(String(100))  # product, service, logistics等
    title = Column(String(255))
    content = Column(Text)
    status = Column(Enum(ComplaintStatus), default=ComplaintStatus.OPEN)
    priority = Column(String(20), default="normal")  # low, normal, high, urgent
    assigned_to = Column(Integer, ForeignKey("users.id"))
    resolution = Column(Text)
    satisfaction_rating = Column(Integer)  # 满意度评分 1-5
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关系
    user = relationship("User", foreign_keys=[user_id], back_populates="complaints")
    assignee = relationship("User", foreign_keys=[assigned_to])


class CustomerServiceSession(Base):
    """客服会话表"""
    __tablename__ = "customer_service_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(100), unique=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    agent_type = Column(String(50), default="ai")  # ai, human
    agent_id = Column(String(100))  # 智能体或人工客服ID
    status = Column(String(20), default="active")  # active, closed, transferred
    start_time = Column(DateTime, default=func.now())
    end_time = Column(DateTime)
    satisfaction_rating = Column(Integer)  # 满意度评分
    tags = Column(JSON)  # 会话标签
    summary = Column(Text)  # 会话总结
    
    # 关系
    user = relationship("User", back_populates="customer_service_sessions")
    messages = relationship("CustomerServiceMessage", back_populates="session")


class CustomerServiceMessage(Base):
    """客服消息表"""
    __tablename__ = "customer_service_messages"

    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(100), ForeignKey("customer_service_sessions.session_id"))
    sender_type = Column(String(20))  # user, agent
    sender_id = Column(String(100))  # 发送者ID
    message_type = Column(String(20), default="text")  # text, image, file, system
    content = Column(Text)
    message_metadata = Column(JSON)  # 消息元数据（文件信息、图片信息等）
    is_read = Column(Boolean, default=False)
    created_at = Column(DateTime, default=func.now())

    # 关系
    session = relationship("CustomerServiceSession", back_populates="messages")
